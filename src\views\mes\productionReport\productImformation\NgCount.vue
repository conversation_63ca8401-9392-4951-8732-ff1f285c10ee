<template>
  <ProChart
    :options="chartOptions"
    :fetch-api="fetchData"
    :tool-buttons="['refresh', 'download', 'table', 'compare']"
    @data-loaded="handleDataLoaded"
    :compare-list="compareList"
    :machines="machines"
    :init-params="initParams"
    id="651535566975045"
    ref="userTable"
    chart-height="222px"
    @export-data="handleExportData"
  />
</template>

<script setup lang="tsx" name="ng统计">
import moment from "moment";
import { productionReportCapacityApi } from "@/api";
import { alramAnalysisApi } from "@/api";
import { useI18n } from "vue-i18n";
const { t } = useI18n();
// 动态获取选项
const machineList = ref<any[]>([]); // 新增机台列表响应式变量
const initParams = ref({
  time: [moment().startOf("day").format("YYYY-MM-DD"), moment().endOf("day").format("YYYY-MM-DD")],
  type: 4
});
const machines = ref<any[]>([]);
const ngTypes = ref<string[]>([]);
const colorPalette = ref<string[]>([]);
const root = document.documentElement;
const chartColor = getComputedStyle(root).getPropertyValue("--el-text-color-regular");

// 动态生成颜色配置
const generateColors = (count: number) => {
  const palette = [
    "#00bfff",
    "#ff4d4f",
    "#fac858",
    "#87d068",
    "#9a60b4",
    "#ff8c00",
    "#1e90ff",
    "#32cd32",
    "#ff1493",
    "#7b68ee",
    "#00fa9a",
    "#ff6347"
  ];
  return palette.slice(0, count);
};

// 图表基础配置
const baseChartOptions = {
  title: {
    text: t("menu.ngStatistics"),
    left: "6%",
    top: 0,
    textStyle: {
      fontSize: 16,
      fontWeight: "bold",
      color: chartColor
    },
    subtext: t("common.compareList.averageNGRate") + ": 0", //"平均NG率：0",
    subtextStyle: {
      color: chartColor
    }
  },
  toolbox: {
    show: true,
    feature: {
      magicType: {
        type: ["line", "bar"],
        title: {
          line: t("productionReport.capacity.switchToLine"), // 切换为折线图
          bar: t("productionReport.capacity.switchToBar") // 切换为柱状图
        }
      },
      saveAsImage: { show: true }
    }
  },
  tooltip: {
    trigger: "axis",
    axisPointer: { type: "shadow" }
  },
  legend: {
    top: 13,
    textStyle: { color: chartColor }
  },
  xAxis: {
    type: "category",
    data: [],
    axisLabel: {
      color: chartColor,
      interval: 0,
      rotate: 45
    }
  },
  yAxis: [
    {
      type: "value",
      name: t("common.compareList.ratio"), //"比率 (%)",
      min: 0,
      axisLabel: {
        color: chartColor,
        formatter: (value: number) => `${value}%`
      },
      splitLine: { lineStyle: { color: "#eee" } }
    },
    {
      type: "value",
      name: t("common.compareList.NGTimes"), //"NG次数",
      min: 0,
      axisLabel: {
        color: chartColor
      },
      splitLine: { lineStyle: { color: "#eee" } },
      position: "right"
    }
  ],
  grid: {
    left: "3%",
    right: "3%",
    bottom: "3%",
    containLabel: true
  },
  series: []
};

const chartOptions = ref({ ...baseChartOptions });

// 动态生成对比列表
const compareList = computed(() => {
  return ngTypes.value.map(type => ({
    label: type,
    value: type
  }));
});
const fetchMachines = async () => {
  try {
    console.log("开始获取机台列表");
    const response = await alramAnalysisApi.getListMesReportData({ Type: 0 });
    const list = response.data.list || [];

    machineList.value = list.map(item => ({
      id: item.MachineName || "未知机台",
      name: item.MachineName || "未知机台"
    }));
    // // 添加：设置默认选中第一个机台
    // if (machineList.value.length > 0) {
    //   searchParam.value.machine = machineList.value[0].id; // 设置默认机台 id
    // }

    console.log("机台列表已更新:", machineList.value);
  } catch (error) {
    console.error("机台列表请求失败:", error);
    ElMessage.error("获取机台列表失败");
  }
};
// 数据获取函数
const fetchData = async (params: any) => {
  try {
    const time = {
      StartDate: moment(params.time[0]).format("YYYY-MM-DD HH:mm:ss").toString(),
      EndDate: moment(params.time[1]).set({ hour: 23, minute: 59, second: 59 }).format("YYYY-MM-DD HH:mm:ss").toString()
    };
    const query = {
      ...time,
      type: params.type
    };
    const { data } = await productionReportCapacityApi.getListMesReportData(query);

    if (!data || !data.list || data.list.length === 0) {
      return {
        data: {
          categories: [],
          seriesData: [],
          isCompare: false
        }
      };
    }

    // 获取时间模式
    const mode = data.list[0].type || "Hour";
    const transformedData = transformData(data.list, mode);
    console.log(transformedData, "transformedData");

    machines.value = transformedData.machines;
    console.log(machines.value, "machines");

    // 普通模式
    if (!params.compareMode) {
      const targetMachine = params.machine || transformedData.machines[0]?.name;

      // 使用新的按NG类别统计的数据转换
      const ngTypeData = transformDataByNgType(data.list, targetMachine);

      if (!ngTypeData.categories.length) {
        return {
          data: {
            categories: [],
            ngQuantities: [],
            ngRates: [],
            isCompare: false
          }
        };
      }

      return {
        data: {
          categories: ngTypeData.categories,
          ngQuantities: ngTypeData.ngQuantities,
          ngRates: ngTypeData.ngRates,
          ngTypeStats: ngTypeData.ngTypeStats,
          isCompare: false
        }
      };
    }

    // 对比模式
    return {
      data: {
        isCompare: true,
        categories: transformedData.categories,
        compare: transformedData.compareData,
        ngTypes: ngTypes.value
      }
    };
  } catch (error) {
    console.error("获取数据失败:", error);
    return {
      data: {
        categories: [],
        seriesData: [],
        isCompare: false
      }
    };
  }
};
// 数据转换函数
function transformData(responseData, timeType) {
  const machineMap = new Map();
  const timeSet = new Set();
  const machineIdSet = new Set();
  const ngTypeSet = new Set();

  let formatPattern;
  switch (timeType) {
    case "Hour":
      formatPattern = "HH:00";
      break;
    case "Mon":
      formatPattern = "MM-DD";
      break;
    case "Year":
      formatPattern = "YYYY-MM";
      break;
    default:
      formatPattern = "HH:00";
  }

  // 遍历数据，构建数据结构
  responseData.forEach(item => {
    const machine = item.machine;
    const ngType = item.ngItem;
    const formattedTime = moment(item.start_time).format(formatPattern);

    if (!machineMap.has(machine)) {
      machineMap.set(machine, new Map());
    }

    const machineData = machineMap.get(machine);
    if (!machineData.has(ngType)) {
      machineData.set(ngType, {
        dataPoints: [],
        ngQuantityPoints: [],
        total: 0,
        totalNgQuantity: 0,
        count: 0
      });
    }

    const ngTypeData = machineData.get(ngType);
    ngTypeData.dataPoints.push({
      time: formattedTime,
      value: item.averageNGRate
    });
    ngTypeData.ngQuantityPoints.push({
      time: formattedTime,
      value: item.ngquantity
    });
    ngTypeData.total += item.averageNGRate;
    ngTypeData.totalNgQuantity += item.ngquantity;
    ngTypeData.count++;

    timeSet.add(formattedTime);
    machineIdSet.add(machine);
    ngTypeSet.add(ngType);
  });

  // 更新NG类型和颜色
  ngTypes.value = Array.from(ngTypeSet);
  colorPalette.value = generateColors(ngTypes.value.length);

  // 对时间点进行排序
  const categories = Array.from(timeSet).sort();

  // 计算各NG类型的平均值
  machineMap.forEach(machineData => {
    machineData.forEach(ngTypeData => {
      ngTypeData.average = (ngTypeData.total / ngTypeData.count).toFixed(1);
      ngTypeData.averageNgQuantity = (ngTypeData.totalNgQuantity / ngTypeData.count).toFixed(1);
    });
  });

  // 构建对比数据
  const compareData = {};
  let compare = [];

  ngTypes.value.forEach(type => {
    compareData[type] = Array.from(machineMap.entries()).map(([machine, ngData]) => ({
      machine,
      data: ngData.has(type) ? ngData.get(type).dataPoints.map(d => d.value) : [],
      ngQuantityData: ngData.has(type) ? ngData.get(type).ngQuantityPoints.map(d => d.value) : [],
      total: ngData.has(type) ? parseFloat(ngData.get(type).average) : 0,
      totalNgQuantity: ngData.has(type) ? parseFloat(ngData.get(type).averageNgQuantity) : 0
    }));
  });
  compare.push(compareData);

  return {
    categories,
    machines: Array.from(machineIdSet).map(id => ({ id, name: id })),
    compareData: compare,
    machineData: machineMap
  };
}

// 新增：按NG类别统计的数据转换函数（仅用于非对比模式）
function transformDataByNgType(responseData, targetMachine) {
  const ngTypeMap = new Map();

  // 遍历数据，按NG类型聚合
  responseData.forEach(item => {
    if (item.machine !== targetMachine) return;

    const ngType = item.ngItem;
    if (!ngTypeMap.has(ngType)) {
      ngTypeMap.set(ngType, {
        totalNgQuantity: 0,
        totalNgRate: 0,
        count: 0
      });
    }

    const ngData = ngTypeMap.get(ngType);
    ngData.totalNgQuantity += item.ngquantity || 0;
    ngData.totalNgRate += item.averageNGRate || 0;
    ngData.count++;
  });

  // 计算平均值并构建结果数组
  const ngTypeStats = Array.from(ngTypeMap.entries()).map(([ngType, data]) => ({
    ngType,
    totalNgQuantity: data.totalNgQuantity,
    averageNgRate: data.count > 0 ? (data.totalNgRate / data.count).toFixed(1) : 0
  }));

  // 按NG次数从高到低排序
  ngTypeStats.sort((a, b) => b.totalNgQuantity - a.totalNgQuantity);

  // 提取分类和数据
  const categories = ngTypeStats.map(item => item.ngType);
  const ngQuantities = ngTypeStats.map(item => item.totalNgQuantity);
  const ngRates = ngTypeStats.map(item => parseFloat(item.averageNgRate.toString()));

  return {
    categories,
    ngQuantities,
    ngRates,
    ngTypeStats
  };
}

// 数据加载回调
const handleDataLoaded = (data: any) => {
  // 普通模式
  if (!data.isCompare) {
    // 计算NG总数和平均NG率
    const totalNgQuantity = data.ngQuantities ? data.ngQuantities.reduce((sum: number, val: number) => sum + val, 0) : 0;
    const averageNgRate =
      data.ngRates && data.ngRates.length > 0
        ? (data.ngRates.reduce((sum: number, val: number) => sum + val, 0) / data.ngRates.length).toFixed(1)
        : "0";

    // 更新图表配置
    chartOptions.value = {
      ...baseChartOptions,
      title: {
        ...baseChartOptions.title,
        subtext: `${t("common.compareList.NGTimes")}：${totalNgQuantity} | ${t("common.compareList.averageNGRate")}：${averageNgRate}%`
      },
      xAxis: {
        ...baseChartOptions.xAxis,
        data: data.categories,
        name: "NG类型"
      },
      yAxis: [
        {
          ...baseChartOptions.yAxis[0],
          name: t("common.compareList.ratio") // NG率 (%)
        },
        {
          ...baseChartOptions.yAxis[1],
          name: t("common.compareList.NGTimes") // NG次数
        }
      ],
      legend: {
        ...baseChartOptions.legend,
        data: ["NG率", "NG次数"]
      },
      tooltip: {
        ...baseChartOptions.tooltip,
        formatter: (params: any) => {
          let html = `<div style="padding:5px;min-width:120px">`;
          html += `<div style="font-weight:bold;margin-bottom:5px;">${params[0].axisValue}</div>`;
          params.forEach((p: any) => {
            const unit = p.seriesName === "NG次数" ? "" : "%";
            html += `
              <div>
                <span style="display:inline-block;width:10px;height:10px;background:${p.color};border-radius:50%"></span>
                ${p.seriesName}: ${p.data}${unit}
              </div>`;
          });
          html += `</div>`;
          return html;
        }
      },
      series: [
        {
          name: "NG率",
          type: "bar",
          data: data.ngRates || [],
          itemStyle: { color: "#ff4d4f" },
          label: {
            show: true,
            position: "top",
            formatter: "{c}%"
          },
          yAxisIndex: 0
        },
        {
          name: "NG次数",
          type: "line",
          data: data.ngQuantities || [],
          itemStyle: { color: "#1890ff" },
          lineStyle: { color: "#1890ff" },
          label: {
            show: true,
            position: "top",
            formatter: "{c}"
          },
          yAxisIndex: 1
        }
      ]
    };
  } else {
    // 对比模式处理
    // 这里可以根据需要实现对比模式的图表展示
    console.log("对比模式数据:", data);
  }
};
// 添加 onMounted 钩子，页面加载时自动触发搜索
onMounted(async () => {
  await fetchMachines(); // 挂载时获取机台
});
const emits = defineEmits(["data-ready", "export-data"]);

const handleExportData = (csvContent: string) => {
  // 父组件可以在这里做一些处理，然后继续传递给爷组件

  // eslint-disable-next-line vue/custom-event-name-casing
  emits("export-data", {
    data: csvContent
  });
};
const userTable = ref<any>(null);

defineExpose({
  tableRef: userTable
});
</script>
